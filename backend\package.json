{"name": "mama-backend", "version": "1.0.0", "description": "Backend API for MAMA - Maternal Health Companion", "main": "start.js", "type": "module", "scripts": {"start": "node start.js", "dev": "nodemon start.js", "test": "jest"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.1", "xss-clean": "^0.1.4"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}